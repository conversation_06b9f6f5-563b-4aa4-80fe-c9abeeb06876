# FastAPI WebSocket Service Dockerfile
FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Copy requirements first for better caching
COPY service/requirements-fastapi.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements-fastapi.txt

# Copy application code
COPY service/ .

# Create necessary directories
RUN mkdir -p /app/logs

# Set permissions
RUN chmod +x /app/frontend.py

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run the FastAPI service
CMD ["python", "-m", "uvicorn", "frontend:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
