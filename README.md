# 🤖 Jarvis Multi-Agent AI System

A sophisticated, voice-enabled multi-agent AI system featuring real-time communication, dynamic tool management, and self-improvement capabilities.

## 🌟 Features

### 🎤 Voice Processing
- **Speech-to-Text**: WhisperX, ElevenLabs, AssemblyAI support
- **Text-to-Speech**: <PERSON><PERSON> TTS, ElevenLabs, pyttsx3 support
- **Real-time Processing**: Streaming audio with WebSocket integration
- **Multiple Providers**: Fallback and quality options

### 🤖 Multi-Agent System
- **4 Specialized Agents**: GPT-4o, Gemma2-7B, Gemini-2.5, Llama3.2-8B
- **AutoGen Framework**: Advanced agent orchestration and communication
- **Dynamic Routing**: Intelligent task delegation based on content analysis
- **Model Flexibility**: OpenRouter and Ollama integration

### 🔧 Dynamic Tool Management
- **MCP Integration**: Smithery registry for tool discovery and installation
- **Safety Scanning**: MCPSafetyScanner for secure tool validation
- **Auto-Installation**: Popular tools installed automatically
- **Sandboxed Execution**: Secure tool execution environment

### 🧠 Self-Improvement (Reflexion)
- **Task Analysis**: Automatic performance evaluation
- **Heuristic Learning**: Extract and apply learned patterns
- **Failure Analysis**: Identify and avoid common failure modes
- **Adaptive Behavior**: Continuous improvement through experience

### 💰 Cost Tracking & Budget Management
- **Real-time Tracking**: Monitor LLM usage costs across all agents
- **Budget Alerts**: Configurable spending limits and warnings
- **Detailed Analytics**: Cost breakdown by agent, model, and session
- **Export Capabilities**: Usage data export for analysis

### 🔍 RAG (Retrieval-Augmented Generation)
- **Qdrant Integration**: High-performance vector database
- **Document Processing**: PDF, DOCX, TXT, MD, JSON, CSV support
- **Semantic Search**: Context-aware information retrieval
- **Auto-Indexing**: Automatic document chunking and embedding

### 🌐 Real-time Communication
- **WebSocket API**: Bi-directional real-time communication
- **Voice Streaming**: Live audio input/output
- **Session Management**: Multi-user session handling
- **Message Types**: Text, voice, system commands, status updates

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Voice         │    │   Agent         │
│   (WebSocket)   │◄──►│   Service       │◄──►│   Service       │
│   Port: 8000    │    │   Port: 8002    │    │   Port: 8001    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │     Qdrant      │    │     Ollama      │
│   (Database)    │    │   (Vectors)     │    │   (Local LLM)   │
│   Port: 5432    │    │   Port: 6333    │    │   Port: 11434   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Docker & Docker Compose
- 8GB+ RAM (16GB recommended)
- GPU support (optional, for better performance)

### 1. Clone and Setup
```bash
git clone <repository-url>
cd jarvis-multi-agent-ai
cp .env.example .env
```

### 2. Configure Environment
Edit `.env` file with your API keys:
```bash
# Required for cloud models
OPENROUTER_API_KEY=your_openrouter_key_here

# Optional for premium voice
ELEVENLABS_API_KEY=your_elevenlabs_key_here

# Voice providers (whisperx, elevenlabs, assemblyai)
STT_PROVIDER=whisperx
TTS_PROVIDER=coqui

# Budget limit per session (USD)
BUDGET_LIMIT=100.00
```

### 3. Start the System
```bash
# Start all services
./scripts/start_system.sh

# Or with production setup
./scripts/start_system.sh --production

# With basic testing
./scripts/start_system.sh --test
```

### 4. Test the System
```bash
# Run comprehensive tests
python scripts/test_system.py

# Or install dependencies first
pip install websockets httpx
python scripts/test_system.py
```

## 📡 API Endpoints

### WebSocket API
- **Main Connection**: `ws://localhost:8000/ws/{session_id}`
- **Message Types**: `voice_input`, `text_input`, `system_command`

### HTTP APIs
- **FastAPI Frontend**: `http://localhost:8000`
- **Agent Service**: `http://localhost:8001`
- **Voice Service**: `http://localhost:8002`

### Health Checks
```bash
curl http://localhost:8000/health  # Frontend
curl http://localhost:8001/health  # Agents
curl http://localhost:8002/health  # Voice
```

## 💬 Usage Examples

### WebSocket Communication
```javascript
const ws = new WebSocket('ws://localhost:8000/ws/my-session');

// Send text message
ws.send(JSON.stringify({
  type: 'text_input',
  data: {
    message: 'Hello Jarvis, what can you help me with?',
    context: {}
  }
}));

// Send voice input (base64 encoded audio)
ws.send(JSON.stringify({
  type: 'voice_input',
  data: {
    audio: 'base64_encoded_audio_data',
    format: 'wav',
    sample_rate: 16000
  }
}));
```

### HTTP API Usage
```bash
# Process a task
curl -X POST http://localhost:8001/tasks/process \
  -H "Content-Type: application/json" \
  -d '{
    "content": "Explain quantum computing",
    "session_id": "test-session"
  }'

# Text-to-speech
curl -X POST http://localhost:8002/tts \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Hello, this is Jarvis speaking",
    "session_id": "test-session"
  }'
```

## 🔧 Configuration

### Agent Configuration
Agents are configured in `service/agent.py`:
- **GPT-4o Agent**: Advanced reasoning and problem-solving
- **Gemma2 Agent**: Fast local processing
- **Gemini Agent**: Research and information gathering
- **Llama Agent**: Critical analysis and review

### Voice Configuration
Configure voice providers in `.env`:
```bash
STT_PROVIDER=whisperx          # whisperx, elevenlabs, assemblyai
TTS_PROVIDER=coqui             # coqui, elevenlabs, pyttsx3
WHISPER_MODEL=base             # tiny, base, small, medium, large
```

### Tool Management
Tools are automatically installed from Smithery registry. Configure in `.env`:
```bash
AUTO_INSTALL_MCP_TOOLS=true
MCP_TOOLS_DIR=./mcp_tools
```

## 📊 Monitoring & Analytics

### System Status
```bash
# Get system status
curl http://localhost:8000/connections

# Get agent metrics
curl http://localhost:8001/metrics

# Get voice processing stats
curl http://localhost:8002/status
```

### Cost Tracking
- Real-time cost monitoring in WebSocket messages
- Budget alerts at 50%, 80%, 90%, and 100%
- Detailed cost breakdown by agent and model
- Export capabilities for analysis

### Logs
```bash
# View service logs
docker-compose logs -f fastapi_ws
docker-compose logs -f agent_service
docker-compose logs -f voice_adapter

# View all logs
docker-compose logs -f
```

## 🛠️ Development

### Project Structure
```
jarvis-multi-agent-ai/
├── service/                 # Core services
│   ├── models/             # Pydantic models
│   ├── agent.py            # Multi-agent system
│   ├── voice.py            # Voice processing
│   ├── retrieval.py        # RAG system
│   ├── mcp_integration.py  # Tool management
│   ├── reflexion.py        # Self-improvement
│   ├── cost_tracking.py    # Budget management
│   └── frontend.py         # WebSocket API
├── scripts/                # Utility scripts
├── data/docs/              # RAG documents
├── logs/                   # Application logs
└── docker-compose.yml      # Service orchestration
```

### Adding New Agents
1. Define agent configuration in `service/agent.py`
2. Add model client initialization
3. Update agent routing logic
4. Test with the system

### Adding New Tools
Tools are automatically discovered and installed from the Smithery MCP registry. For custom tools:
1. Create tool package following MCP standards
2. Register with Smithery or install locally
3. Configure safety scanning rules

## 🔒 Security

- **Sandboxed Execution**: Tools run in isolated environments
- **Safety Scanning**: All tools validated before installation
- **API Key Management**: Secure environment variable handling
- **Budget Controls**: Prevent runaway costs
- **Input Validation**: Comprehensive request validation

## 🚨 Troubleshooting

### Common Issues

**Services won't start:**
```bash
# Check Docker status
docker info

# Check logs
docker-compose logs

# Restart services
docker-compose restart
```

**Voice processing fails:**
```bash
# Check voice service logs
docker-compose logs voice_adapter

# Test voice endpoints
curl http://localhost:8002/providers
```

**Agents not responding:**
```bash
# Check agent service
curl http://localhost:8001/agents

# Verify model availability
docker-compose exec ollama ollama list
```

**High costs:**
- Check budget settings in `.env`
- Monitor usage at `/metrics` endpoints
- Consider using local models (Ollama) for development

## 📈 Performance Optimization

### For Development
- Use local models (Ollama) to reduce costs
- Reduce `MAX_TURNS` in `.env`
- Use smaller voice models

### For Production
- Enable GPU support for faster processing
- Use Redis for session caching
- Configure Nginx reverse proxy
- Set up monitoring and alerting

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **AutoGen**: Microsoft's multi-agent framework
- **Qdrant**: High-performance vector database
- **FastAPI**: Modern web framework
- **Ollama**: Local LLM serving
- **Smithery**: MCP tool registry

---

**Built with ❤️ for the future of AI interaction**
