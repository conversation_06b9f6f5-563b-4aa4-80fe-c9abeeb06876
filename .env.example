# Jarvis Multi-Agent AI System - Environment Configuration

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
POSTGRES_PASSWORD=jarvis_secure_password_2024
DATABASE_URL=postgresql://jarvis_user:jarvis_secure_password_2024@localhost:5432/jarvis

# =============================================================================
# VECTOR DATABASE CONFIGURATION
# =============================================================================
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=

# =============================================================================
# LLM API KEYS
# =============================================================================
# OpenRouter API Key for GPT-4o and Gemini models
OPENROUTER_API_KEY=your_openrouter_api_key_here

# ElevenLabs API Key for premium TTS (optional)
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here

# OpenAI API Key (if using direct OpenAI instead of OpenRouter)
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic API Key (if using Claude directly)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# =============================================================================
# VOICE PROCESSING CONFIGURATION
# =============================================================================
# STT Provider: whisperx, faster_whisper, elevenlabs, assemblyai
STT_PROVIDER=faster_whisper

# TTS Provider: coqui, elevenlabs, pyttsx3
TTS_PROVIDER=coqui

# Whisper model size: tiny, base, small, medium, large, large-v2, large-v3, distil-large-v3
WHISPER_MODEL=large-v3

# Faster-Whisper specific settings
FASTER_WHISPER_BATCH_SIZE=16
FASTER_WHISPER_USE_BATCHED=true
FASTER_WHISPER_COMPUTE_TYPE=auto

# Coqui TTS model
COQUI_MODEL=tts_models/en/ljspeech/tacotron2-DDC

# AssemblyAI API Key (if using AssemblyAI STT)
ASSEMBLYAI_API_KEY=your_assemblyai_api_key_here

# =============================================================================
# SYSTEM CONFIGURATION
# =============================================================================
# Maximum conversation turns per session
MAX_TURNS=15

# Budget limit in USD for LLM usage per session
BUDGET_LIMIT=100.00

# Log level: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# Environment: development, staging, production
ENVIRONMENT=development

# =============================================================================
# NETWORK CONFIGURATION
# =============================================================================
# CORS origins for FastAPI (comma-separated)
CORS_ORIGINS=http://localhost:3000,http://localhost:8080,http://127.0.0.1:3000

# Service URLs (for production deployment)
AGENT_SERVICE_URL=http://agent_service:8001
VOICE_SERVICE_URL=http://voice_adapter:8002
FASTAPI_SERVICE_URL=http://fastapi_ws:8000

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT Secret for session management
JWT_SECRET=your_jwt_secret_key_here_make_it_long_and_random

# Session timeout in minutes
SESSION_TIMEOUT=60

# API rate limiting (requests per minute)
RATE_LIMIT=100

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================
# Enable metrics collection
ENABLE_METRICS=true

# Prometheus metrics port
METRICS_PORT=9090

# Sentry DSN for error tracking (optional)
SENTRY_DSN=your_sentry_dsn_here

# =============================================================================
# STORAGE CONFIGURATION
# =============================================================================
# File storage path for RAG documents
DOCS_STORAGE_PATH=./data/docs

# Maximum file size for uploads (in MB)
MAX_FILE_SIZE_MB=50

# Supported file types for RAG (comma-separated)
SUPPORTED_FILE_TYPES=.txt,.pdf,.docx,.md,.json,.csv

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================
# Redis URL for caching
REDIS_URL=redis://localhost:6379/0

# Cache TTL in seconds
CACHE_TTL=3600

# =============================================================================
# OLLAMA CONFIGURATION
# =============================================================================
# Ollama service URL
OLLAMA_URL=http://localhost:11434

# Models to pull on startup (comma-separated)
OLLAMA_MODELS=gemma2:7b,llama3.2:8b

# =============================================================================
# MCP CONFIGURATION
# =============================================================================
# Smithery registry URL
SMITHERY_REGISTRY_URL=https://smithery.ai/registry

# MCP tools directory
MCP_TOOLS_DIR=./mcp_tools

# Auto-install popular tools on startup
AUTO_INSTALL_MCP_TOOLS=true

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================
# Enable debug mode
DEBUG=false

# Enable hot reload for development
HOT_RELOAD=true

# Enable detailed SQL logging
SQL_DEBUG=false

# Enable request/response logging
HTTP_DEBUG=false

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
# Enable automatic database backups
ENABLE_BACKUPS=true

# Backup schedule (cron format)
BACKUP_SCHEDULE=0 2 * * *

# Backup retention days
BACKUP_RETENTION_DAYS=30

# Backup storage path
BACKUP_PATH=./backups

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================
# Database connection pool size
DB_POOL_SIZE=20

# Maximum database connections
DB_MAX_CONNECTIONS=100

# WebSocket connection limit
WS_MAX_CONNECTIONS=1000

# Worker processes for FastAPI
WORKERS=4

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Enable experimental features
ENABLE_EXPERIMENTAL=false

# Enable voice activity detection
ENABLE_VAD=true

# Enable real-time transcription
ENABLE_REAL_TIME_STT=true

# Enable streaming responses
ENABLE_STREAMING=true

# Enable cost tracking
ENABLE_COST_TRACKING=true

# Enable reflexion learning
ENABLE_REFLEXION=true
